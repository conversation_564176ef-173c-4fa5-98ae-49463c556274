<?xml version="1.0" encoding="UTF-8"?>
<phpunit
    bootstrap="tests/bootstrap.php"
    backupGlobals="false"
    colors="true"
    convertErrorsToExceptions="true"
    convertNoticesToExceptions="true"
    convertWarningsToExceptions="true"
    processIsolation="false"
    stopOnFailure="false"
    verbose="true"
>
    <testsuites>
        <testsuite name="Unit Tests">
            <directory>./tests/unit/</directory>
        </testsuite>
        <testsuite name="Integration Tests">
            <directory>./tests/integration/</directory>
        </testsuite>
    </testsuites>
    
    <filter>
        <whitelist processUncoveredFilesFromWhitelist="true">
            <directory suffix=".php">./</directory>
            <exclude>
                <directory>./tests/</directory>
                <directory>./vendor/</directory>
                <file>./index.php</file>
            </exclude>
        </whitelist>
    </filter>
    
    <logging>
        <log type="coverage-html" target="./tests/coverage/html"/>
        <log type="coverage-clover" target="./tests/coverage/clover.xml"/>
    </logging>
    
    <php>
        <const name="WP_TESTS_DOMAIN" value="example.org" />
        <const name="WP_TESTS_EMAIL" value="<EMAIL>" />
        <const name="WP_TESTS_TITLE" value="Test Blog" />
        <const name="WP_PHP_BINARY" value="php" />
        <const name="WP_TESTS_FORCE_KNOWN_BUGS" value="true" />
        <server name="SERVER_NAME" value="http://example.org"/>
    </php>
</phpunit>
