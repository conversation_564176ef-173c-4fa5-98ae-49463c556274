{"version": 1, "defects": {"GravityFormElementor\\Tests\\Unit\\BasicTest::test_brain_monkey_works": 4, "GravityFormElementor\\Tests\\Unit\\BasicTest::test_wordpress_function_mocking": 4, "GravityFormElementor\\Tests\\Unit\\PluginTest::test_plugin_constants_defined": 4, "GravityFormElementor\\Tests\\Unit\\PluginTest::test_dependency_check_with_missing_elementor": 4, "GravityFormElementor\\Tests\\Unit\\PluginTest::test_dependency_check_with_missing_gravity_forms": 4, "GravityFormElementor\\Tests\\Unit\\PluginTest::test_dependency_check_with_all_dependencies": 4, "GravityFormElementor\\Tests\\Unit\\PluginTest::test_elementor_version_compatibility": 4, "GravityFormElementor\\Tests\\Unit\\PluginTest::test_elementor_version_incompatibility": 4, "GravityFormElementor\\Tests\\Unit\\PluginTest::test_php_version_compatibility": 4, "GravityFormElementor\\Tests\\Unit\\PluginTest::test_php_version_incompatibility": 4, "GravityFormElementor\\Tests\\Unit\\WidgetTest::test_widget_basic_properties": 4, "GravityFormElementor\\Tests\\Unit\\WidgetTest::test_get_forms_select_options_without_gfapi": 4, "GravityFormElementor\\Tests\\Unit\\WidgetTest::test_get_forms_select_options_with_forms": 4, "GravityFormElementor\\Tests\\Unit\\WidgetTest::test_get_forms_select_options_with_no_forms": 4, "GravityFormElementor\\Tests\\Unit\\WidgetTest::test_get_form_settings": 4, "GravityFormElementor\\Tests\\Unit\\WidgetTest::test_get_form_settings_invalid_form": 4, "GravityFormElementor\\Tests\\Unit\\WidgetTest::test_get_form_settings_without_gfapi": 4}, "times": {"GravityFormElementor\\Tests\\Unit\\BasicTest::test_phpunit_works": 0.067, "GravityFormElementor\\Tests\\Unit\\BasicTest::test_brain_monkey_works": 0.009, "GravityFormElementor\\Tests\\Unit\\BasicTest::test_plugin_constants_defined": 0, "GravityFormElementor\\Tests\\Unit\\BasicTest::test_mock_classes_available": 0, "GravityFormElementor\\Tests\\Unit\\BasicTest::test_gfapi_mock": 0.006, "GravityFormElementor\\Tests\\Unit\\BasicTest::test_version_comparison": 0.001, "GravityFormElementor\\Tests\\Unit\\BasicTest::test_array_operations": 0.002, "GravityFormElementor\\Tests\\Unit\\BasicTest::test_string_operations": 0.003, "GravityFormElementor\\Tests\\Unit\\BasicTest::test_wordpress_function_mocking": 0, "GravityFormElementor\\Tests\\Unit\\BasicTest::test_dependency_checking_logic": 0.001, "GravityFormElementor\\Tests\\Unit\\PluginTest::test_plugin_constants_defined": 0.001, "GravityFormElementor\\Tests\\Unit\\PluginTest::test_dependency_check_with_missing_elementor": 0, "GravityFormElementor\\Tests\\Unit\\PluginTest::test_dependency_check_with_missing_gravity_forms": 0, "GravityFormElementor\\Tests\\Unit\\PluginTest::test_dependency_check_with_all_dependencies": 0, "GravityFormElementor\\Tests\\Unit\\PluginTest::test_elementor_version_compatibility": 0, "GravityFormElementor\\Tests\\Unit\\PluginTest::test_elementor_version_incompatibility": 0.001, "GravityFormElementor\\Tests\\Unit\\PluginTest::test_php_version_compatibility": 0.001, "GravityFormElementor\\Tests\\Unit\\PluginTest::test_php_version_incompatibility": 0, "GravityFormElementor\\Tests\\Unit\\WidgetTest::test_widget_basic_properties": 0, "GravityFormElementor\\Tests\\Unit\\WidgetTest::test_get_forms_select_options_without_gfapi": 0, "GravityFormElementor\\Tests\\Unit\\WidgetTest::test_get_forms_select_options_with_forms": 0, "GravityFormElementor\\Tests\\Unit\\WidgetTest::test_get_forms_select_options_with_no_forms": 0, "GravityFormElementor\\Tests\\Unit\\WidgetTest::test_get_form_settings": 0, "GravityFormElementor\\Tests\\Unit\\WidgetTest::test_get_form_settings_invalid_form": 0, "GravityFormElementor\\Tests\\Unit\\WidgetTest::test_get_form_settings_without_gfapi": 0}}