{"version": 1, "defects": {"GravityFormElementor\\Tests\\Unit\\BasicTest::test_brain_monkey_works": 4, "GravityFormElementor\\Tests\\Unit\\BasicTest::test_wordpress_function_mocking": 4, "GravityFormElementor\\Tests\\Unit\\PluginTest::test_plugin_constants_defined": 4, "GravityFormElementor\\Tests\\Unit\\PluginTest::test_dependency_check_with_missing_elementor": 4, "GravityFormElementor\\Tests\\Unit\\PluginTest::test_dependency_check_with_missing_gravity_forms": 4, "GravityFormElementor\\Tests\\Unit\\PluginTest::test_dependency_check_with_all_dependencies": 4, "GravityFormElementor\\Tests\\Unit\\PluginTest::test_elementor_version_compatibility": 4, "GravityFormElementor\\Tests\\Unit\\PluginTest::test_elementor_version_incompatibility": 4, "GravityFormElementor\\Tests\\Unit\\PluginTest::test_php_version_compatibility": 4, "GravityFormElementor\\Tests\\Unit\\PluginTest::test_php_version_incompatibility": 4, "GravityFormElementor\\Tests\\Unit\\WidgetTest::test_widget_basic_properties": 4, "GravityFormElementor\\Tests\\Unit\\WidgetTest::test_get_forms_select_options_without_gfapi": 4, "GravityFormElementor\\Tests\\Unit\\WidgetTest::test_get_forms_select_options_with_forms": 4, "GravityFormElementor\\Tests\\Unit\\WidgetTest::test_get_forms_select_options_with_no_forms": 4, "GravityFormElementor\\Tests\\Unit\\WidgetTest::test_get_form_settings": 4, "GravityFormElementor\\Tests\\Unit\\WidgetTest::test_get_form_settings_invalid_form": 4, "GravityFormElementor\\Tests\\Unit\\WidgetTest::test_get_form_settings_without_gfapi": 4, "GravityFormElementor\\Tests\\Unit\\PluginFunctionsTest::test_dependency_check_missing_elementor": 4, "GravityFormElementor\\Tests\\Unit\\PluginFunctionsTest::test_dependency_check_missing_gravity_forms": 4, "GravityFormElementor\\Tests\\Unit\\PluginFunctionsTest::test_dependency_check_all_present": 4, "GravityFormElementor\\Tests\\Unit\\PluginFunctionsTest::test_php_version_compatibility": 4, "GravityFormElementor\\Tests\\Unit\\PluginFunctionsTest::test_elementor_version_no_version_defined": 4, "GravityFormElementor\\Tests\\Unit\\PluginFunctionsTest::test_elementor_version_compatible": 4, "GravityFormElementor\\Tests\\Unit\\PluginFunctionsTest::test_admin_notice_missing_dependencies": 4, "GravityFormElementor\\Tests\\Unit\\PluginFunctionsTest::test_admin_notice_php_version": 4, "GravityFormElementor\\Tests\\Unit\\PluginFunctionsTest::test_widget_styles_registration": 4, "GravityFormElementor\\Tests\\Unit\\PluginFunctionsTest::test_plugin_init_missing_php_version": 4, "GravityFormElementor\\Tests\\Unit\\PluginFunctionsTest::test_plugin_init_all_requirements_met": 4, "GravityFormElementor\\Tests\\Unit\\PluginFunctionsTest::test_all_functions_exist": 3, "GravityFormElementor\\Tests\\Unit\\WidgetFunctionalityTest::test_get_form_settings": 4, "GravityFormElementor\\Tests\\Unit\\WidgetFunctionalityTest::test_widget_render_method": 4, "GravityFormElementor\\Tests\\Unit\\WidgetFunctionalityTest::test_widget_error_handling": 4, "GravityFormElementor\\Tests\\Unit\\WidgetFunctionalityTest::test_widget_with_different_forms": 4}, "times": {"GravityFormElementor\\Tests\\Unit\\BasicTest::test_phpunit_works": 0.004, "GravityFormElementor\\Tests\\Unit\\BasicTest::test_brain_monkey_works": 0.009, "GravityFormElementor\\Tests\\Unit\\BasicTest::test_plugin_constants_defined": 0, "GravityFormElementor\\Tests\\Unit\\BasicTest::test_mock_classes_available": 0, "GravityFormElementor\\Tests\\Unit\\BasicTest::test_gfapi_mock": 0.001, "GravityFormElementor\\Tests\\Unit\\BasicTest::test_version_comparison": 0, "GravityFormElementor\\Tests\\Unit\\BasicTest::test_array_operations": 0, "GravityFormElementor\\Tests\\Unit\\BasicTest::test_string_operations": 0, "GravityFormElementor\\Tests\\Unit\\BasicTest::test_wordpress_function_mocking": 0, "GravityFormElementor\\Tests\\Unit\\BasicTest::test_dependency_checking_logic": 0, "GravityFormElementor\\Tests\\Unit\\PluginTest::test_plugin_constants_defined": 0.001, "GravityFormElementor\\Tests\\Unit\\PluginTest::test_dependency_check_with_missing_elementor": 0, "GravityFormElementor\\Tests\\Unit\\PluginTest::test_dependency_check_with_missing_gravity_forms": 0, "GravityFormElementor\\Tests\\Unit\\PluginTest::test_dependency_check_with_all_dependencies": 0, "GravityFormElementor\\Tests\\Unit\\PluginTest::test_elementor_version_compatibility": 0, "GravityFormElementor\\Tests\\Unit\\PluginTest::test_elementor_version_incompatibility": 0.001, "GravityFormElementor\\Tests\\Unit\\PluginTest::test_php_version_compatibility": 0.001, "GravityFormElementor\\Tests\\Unit\\PluginTest::test_php_version_incompatibility": 0, "GravityFormElementor\\Tests\\Unit\\WidgetTest::test_widget_basic_properties": 0, "GravityFormElementor\\Tests\\Unit\\WidgetTest::test_get_forms_select_options_without_gfapi": 0, "GravityFormElementor\\Tests\\Unit\\WidgetTest::test_get_forms_select_options_with_forms": 0, "GravityFormElementor\\Tests\\Unit\\WidgetTest::test_get_forms_select_options_with_no_forms": 0, "GravityFormElementor\\Tests\\Unit\\WidgetTest::test_get_form_settings": 0, "GravityFormElementor\\Tests\\Unit\\WidgetTest::test_get_form_settings_invalid_form": 0, "GravityFormElementor\\Tests\\Unit\\WidgetTest::test_get_form_settings_without_gfapi": 0, "GravityFormElementor\\Tests\\Unit\\BasicTest::test_wordpress_mocks_work": 0, "GravityFormElementor\\Tests\\Unit\\BasicTest::test_wordpress_function_usage": 0, "GravityFormElementor\\Tests\\Unit\\PluginFunctionsTest::test_plugin_constants_defined": 0, "GravityFormElementor\\Tests\\Unit\\PluginFunctionsTest::test_dependency_check_missing_elementor": 0.001, "GravityFormElementor\\Tests\\Unit\\PluginFunctionsTest::test_dependency_check_missing_gravity_forms": 0, "GravityFormElementor\\Tests\\Unit\\PluginFunctionsTest::test_dependency_check_all_present": 0, "GravityFormElementor\\Tests\\Unit\\PluginFunctionsTest::test_php_version_compatibility": 0, "GravityFormElementor\\Tests\\Unit\\PluginFunctionsTest::test_elementor_version_no_version_defined": 0, "GravityFormElementor\\Tests\\Unit\\PluginFunctionsTest::test_elementor_version_compatible": 0, "GravityFormElementor\\Tests\\Unit\\PluginFunctionsTest::test_admin_notice_missing_dependencies": 0, "GravityFormElementor\\Tests\\Unit\\PluginFunctionsTest::test_admin_notice_php_version": 0, "GravityFormElementor\\Tests\\Unit\\PluginFunctionsTest::test_widget_styles_registration": 0, "GravityFormElementor\\Tests\\Unit\\PluginFunctionsTest::test_plugin_init_missing_php_version": 0, "GravityFormElementor\\Tests\\Unit\\PluginFunctionsTest::test_plugin_init_all_requirements_met": 0, "GravityFormElementor\\Tests\\Unit\\PluginFunctionsTest::test_all_functions_exist": 0, "GravityFormElementor\\Tests\\Unit\\PluginFunctionsTest::test_mock_classes_available": 0, "GravityFormElementor\\Tests\\Unit\\PluginFunctionsTest::test_gfapi_mock_functionality": 0, "GravityFormElementor\\Tests\\Unit\\WidgetFunctionalityTest::test_widget_basic_properties": 0.001, "GravityFormElementor\\Tests\\Unit\\WidgetFunctionalityTest::test_get_forms_select_options_without_gfapi": 0, "GravityFormElementor\\Tests\\Unit\\WidgetFunctionalityTest::test_get_forms_select_options_with_forms": 0, "GravityFormElementor\\Tests\\Unit\\WidgetFunctionalityTest::test_get_form_settings": 0, "GravityFormElementor\\Tests\\Unit\\WidgetFunctionalityTest::test_get_form_settings_invalid_form": 0, "GravityFormElementor\\Tests\\Unit\\WidgetFunctionalityTest::test_get_form_setting_labels": 0, "GravityFormElementor\\Tests\\Unit\\WidgetFunctionalityTest::test_widget_settings": 0, "GravityFormElementor\\Tests\\Unit\\WidgetFunctionalityTest::test_widget_render_method": 0, "GravityFormElementor\\Tests\\Unit\\WidgetFunctionalityTest::test_widget_control_methods_exist": 0, "GravityFormElementor\\Tests\\Unit\\WidgetFunctionalityTest::test_widget_inheritance": 0, "GravityFormElementor\\Tests\\Unit\\WidgetFunctionalityTest::test_form_settings_integration": 0, "GravityFormElementor\\Tests\\Unit\\WidgetFunctionalityTest::test_widget_error_handling": 0, "GravityFormElementor\\Tests\\Unit\\WidgetFunctionalityTest::test_widget_with_different_forms": 0, "GravityFormElementor\\Tests\\Unit\\ComprehensiveTest::test_plugin_configuration": 0.001, "GravityFormElementor\\Tests\\Unit\\ComprehensiveTest::test_core_functions_exist": 0, "GravityFormElementor\\Tests\\Unit\\ComprehensiveTest::test_php_version_compatibility": 0, "GravityFormElementor\\Tests\\Unit\\ComprehensiveTest::test_dependency_checking": 0, "GravityFormElementor\\Tests\\Unit\\ComprehensiveTest::test_widget_class": 0, "GravityFormElementor\\Tests\\Unit\\ComprehensiveTest::test_widget_settings": 0, "GravityFormElementor\\Tests\\Unit\\ComprehensiveTest::test_mock_classes": 0, "GravityFormElementor\\Tests\\Unit\\ComprehensiveTest::test_gfapi_functionality": 0, "GravityFormElementor\\Tests\\Unit\\ComprehensiveTest::test_wordpress_function_mocks": 0.001, "GravityFormElementor\\Tests\\Unit\\ComprehensiveTest::test_style_registration": 0, "GravityFormElementor\\Tests\\Unit\\ComprehensiveTest::test_widget_methods": 0, "GravityFormElementor\\Tests\\Unit\\ComprehensiveTest::test_widget_inheritance": 0, "GravityFormElementor\\Tests\\Unit\\ComprehensiveTest::test_data_operations": 0, "GravityFormElementor\\Tests\\Unit\\ComprehensiveTest::test_version_comparison": 0, "GravityFormElementor\\Tests\\Unit\\ComprehensiveTest::test_error_handling": 0}}