<?php
/**
 * Unit tests for main plugin functionality
 */

namespace GravityFormElementor\Tests\Unit;

use PHPUnit\Framework\TestCase;
use Brain\Monkey;
use Brain\Monkey\Functions;

class PluginTest extends TestCase {

    protected function setUp(): void {
        parent::setUp();
        Monkey\setUp();
        
        // Mock WordPress functions
        Functions\when('defined')->justReturn(true);
        Functions\when('did_action')->justReturn(true);
        Functions\when('class_exists')->justReturn(true);
        Functions\when('version_compare')->justReturn(true);
        Functions\when('esc_html__')->returnArg();
        Functions\when('esc_html')->returnArg();
        Functions\when('plugins_url')->justReturn('http://example.com/plugin');
        Functions\when('__DIR__')->justReturn('/path/to/plugin');
        Functions\when('add_action')->justReturn(true);
        Functions\when('wp_register_style')->justReturn(true);
        Functions\when('wp_enqueue_style')->justReturn(true);
    }

    protected function tearDown(): void {
        Monkey\tearDown();
        parent::tearDown();
    }

    /**
     * Test plugin constants are defined
     */
    public function test_plugin_constants_defined() {
        // These should be defined when the plugin loads
        $this->assertTrue(defined('GF_ELEMENTOR_WIDGET_VERSION'));
        $this->assertTrue(defined('GF_ELEMENTOR_WIDGET_MINIMUM_ELEMENTOR_VERSION'));
        $this->assertTrue(defined('GF_ELEMENTOR_WIDGET_MINIMUM_PHP_VERSION'));
    }

    /**
     * Test dependency checking function
     */
    public function test_dependency_check_with_missing_elementor() {
        Functions\when('did_action')->with('elementor/loaded')->justReturn(false);
        Functions\when('class_exists')->with('GFForms')->justReturn(true);
        
        $missing = gf_elementor_widget_check_dependencies();
        
        $this->assertContains('Elementor', $missing);
        $this->assertNotContains('Gravity Forms', $missing);
    }

    /**
     * Test dependency checking function with missing Gravity Forms
     */
    public function test_dependency_check_with_missing_gravity_forms() {
        Functions\when('did_action')->with('elementor/loaded')->justReturn(true);
        Functions\when('class_exists')->with('GFForms')->justReturn(false);
        
        $missing = gf_elementor_widget_check_dependencies();
        
        $this->assertNotContains('Elementor', $missing);
        $this->assertContains('Gravity Forms', $missing);
    }

    /**
     * Test dependency checking function with all dependencies present
     */
    public function test_dependency_check_with_all_dependencies() {
        Functions\when('did_action')->with('elementor/loaded')->justReturn(true);
        Functions\when('class_exists')->with('GFForms')->justReturn(true);
        
        $missing = gf_elementor_widget_check_dependencies();
        
        $this->assertEmpty($missing);
    }

    /**
     * Test Elementor version compatibility check
     */
    public function test_elementor_version_compatibility() {
        Functions\when('defined')->with('ELEMENTOR_VERSION')->justReturn(true);
        Functions\when('version_compare')
            ->with('3.5.0', GF_ELEMENTOR_WIDGET_MINIMUM_ELEMENTOR_VERSION, '<')
            ->justReturn(false);
        
        $compatible = gf_elementor_widget_check_elementor_version();
        
        $this->assertTrue($compatible);
    }

    /**
     * Test Elementor version incompatibility
     */
    public function test_elementor_version_incompatibility() {
        Functions\when('defined')->with('ELEMENTOR_VERSION')->justReturn(true);
        Functions\when('version_compare')
            ->with('2.9.0', GF_ELEMENTOR_WIDGET_MINIMUM_ELEMENTOR_VERSION, '<')
            ->justReturn(true);
        
        $compatible = gf_elementor_widget_check_elementor_version();
        
        $this->assertFalse($compatible);
    }

    /**
     * Test PHP version compatibility check
     */
    public function test_php_version_compatibility() {
        Functions\when('version_compare')
            ->with(PHP_VERSION, GF_ELEMENTOR_WIDGET_MINIMUM_PHP_VERSION, '<')
            ->justReturn(false);
        
        $compatible = gf_elementor_widget_check_php_version();
        
        $this->assertTrue($compatible);
    }

    /**
     * Test PHP version incompatibility
     */
    public function test_php_version_incompatibility() {
        Functions\when('version_compare')
            ->with('7.3', GF_ELEMENTOR_WIDGET_MINIMUM_PHP_VERSION, '<')
            ->justReturn(true);
        
        $compatible = gf_elementor_widget_check_php_version();
        
        $this->assertFalse($compatible);
    }
}
