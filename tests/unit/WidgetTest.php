<?php
/**
 * Unit tests for Elementor GF Widget
 */

namespace GravityFormElementor\Tests\Unit;

use PHPUnit\Framework\TestCase;
use Brain\Monkey;
use Brain\Monkey\Functions;

class WidgetTest extends TestCase {

    protected $widget;

    protected function setUp(): void {
        parent::setUp();
        Monkey\setUp();
        
        // Mock WordPress functions
        Functions\when('esc_html__')->returnArg();
        Functions\when('esc_html')->returnArg();
        Functions\when('class_exists')->justReturn(true);
        Functions\when('is_wp_error')->justReturn(false);
        
        // Load the widget class
        require_once dirname(dirname(__DIR__)) . '/widgets/gf-widget.php';
        
        // Create widget instance
        $this->widget = new \Elementor_GF_Widget();
    }

    protected function tearDown(): void {
        Monkey\tearDown();
        parent::tearDown();
    }

    /**
     * Test widget basic properties
     */
    public function test_widget_basic_properties() {
        $this->assertEquals('gf_widget', $this->widget->get_name());
        $this->assertEquals('Gravity Form', $this->widget->get_title());
        $this->assertEquals('eicon-form-horizontal', $this->widget->get_icon());
        
        $categories = $this->widget->get_categories();
        $this->assertContains('impact-hub-elements', $categories);
        $this->assertContains('gravity-forms', $categories);
        
        $keywords = $this->widget->get_keywords();
        $this->assertContains('form', $keywords);
        $this->assertContains('gravity', $keywords);
    }

    /**
     * Test form options retrieval when GFAPI is not available
     */
    public function test_get_forms_select_options_without_gfapi() {
        Functions\when('class_exists')->with('GFAPI')->justReturn(false);
        
        $reflection = new \ReflectionClass($this->widget);
        $method = $reflection->getMethod('get_forms_select_options');
        $method->setAccessible(true);
        
        $options = $method->invoke($this->widget);
        
        $this->assertArrayHasKey('', $options);
        $this->assertEquals('Gravity Forms not available', $options['']);
    }

    /**
     * Test form options retrieval with available forms
     */
    public function test_get_forms_select_options_with_forms() {
        Functions\when('class_exists')->with('GFAPI')->justReturn(true);
        
        // Mock GFAPI::get_forms()
        $mock_forms = [
            ['id' => '1', 'title' => 'Contact Form'],
            ['id' => '2', 'title' => 'Newsletter Signup']
        ];
        
        Functions\when('GFAPI::get_forms')->justReturn($mock_forms);
        
        $reflection = new \ReflectionClass($this->widget);
        $method = $reflection->getMethod('get_forms_select_options');
        $method->setAccessible(true);
        
        $options = $method->invoke($this->widget);
        
        $this->assertArrayHasKey('1', $options);
        $this->assertArrayHasKey('2', $options);
        $this->assertEquals('Contact Form', $options['1']);
        $this->assertEquals('Newsletter Signup', $options['2']);
    }

    /**
     * Test form options retrieval with no forms
     */
    public function test_get_forms_select_options_with_no_forms() {
        Functions\when('class_exists')->with('GFAPI')->justReturn(true);
        Functions\when('GFAPI::get_forms')->justReturn([]);
        
        $reflection = new \ReflectionClass($this->widget);
        $method = $reflection->getMethod('get_forms_select_options');
        $method->setAccessible(true);
        
        $options = $method->invoke($this->widget);
        
        $this->assertArrayHasKey('', $options);
        $this->assertEquals('No forms found', $options['']);
    }

    /**
     * Test form settings retrieval
     */
    public function test_get_form_settings() {
        Functions\when('class_exists')->with('GFAPI')->justReturn(true);
        
        $mock_form = [
            'id' => '1',
            'title' => 'Test Form',
            'labelPlacement' => 'top_label',
            'descriptionPlacement' => 'below',
            'subLabelPlacement' => 'below',
            'requiredIndicator' => 'asterisk',
            'customRequiredIndicator' => '',
            'validationSummary' => false,
            'cssClass' => 'test-class',
            'enableHoneypot' => true,
            'enableAnimation' => false,
            'markupVersion' => 2,
            'description' => 'Test description'
        ];
        
        Functions\when('GFAPI::get_form')->with('1')->justReturn($mock_form);
        
        $reflection = new \ReflectionClass($this->widget);
        $method = $reflection->getMethod('get_form_settings');
        $method->setAccessible(true);
        
        $settings = $method->invoke($this->widget, '1');
        
        $this->assertEquals('top_label', $settings['labelPlacement']);
        $this->assertEquals('below', $settings['descriptionPlacement']);
        $this->assertEquals('test-class', $settings['cssClass']);
        $this->assertTrue($settings['enableHoneypot']);
        $this->assertEquals('Test Form', $settings['title']);
    }

    /**
     * Test form settings retrieval with invalid form ID
     */
    public function test_get_form_settings_invalid_form() {
        Functions\when('class_exists')->with('GFAPI')->justReturn(true);
        Functions\when('GFAPI::get_form')->with('999')->justReturn(false);
        
        $reflection = new \ReflectionClass($this->widget);
        $method = $reflection->getMethod('get_form_settings');
        $method->setAccessible(true);
        
        $settings = $method->invoke($this->widget, '999');
        
        $this->assertEmpty($settings);
    }

    /**
     * Test form settings retrieval without GFAPI
     */
    public function test_get_form_settings_without_gfapi() {
        Functions\when('class_exists')->with('GFAPI')->justReturn(false);
        
        $reflection = new \ReflectionClass($this->widget);
        $method = $reflection->getMethod('get_form_settings');
        $method->setAccessible(true);
        
        $settings = $method->invoke($this->widget, '1');
        
        $this->assertEmpty($settings);
    }
}
