<?php
/**
 * PHPUnit bootstrap file for Unit Tests only
 * This bootstrap does NOT load WordPress - it uses mocks instead
 */

// Composer autoloader
if (file_exists(dirname(__DIR__) . '/vendor/autoload.php')) {
    require_once dirname(__DIR__) . '/vendor/autoload.php';
}

// Initialize Brain Monkey for mocking WordPress functions
if (class_exists('Brain\Monkey\setUp')) {
    Brain\Monkey\setUp();
}

// Define WordPress constants that our plugin expects
if (!defined('ABSPATH')) {
    define('ABSPATH', '/fake/wordpress/path/');
}

if (!defined('GF_ELEMENTOR_WIDGET_VERSION')) {
    define('GF_ELEMENTOR_WIDGET_VERSION', '1.0.2');
}

if (!defined('GF_ELEMENTOR_WIDGET_MINIMUM_ELEMENTOR_VERSION')) {
    define('GF_ELEMENTOR_WIDGET_MINIMUM_ELEMENTOR_VERSION', '3.0.0');
}

if (!defined('GF_ELEMENTOR_WIDGET_MINIMUM_PHP_VERSION')) {
    define('GF_ELEMENTOR_WIDGET_MINIMUM_PHP_VERSION', '7.4');
}

// Load our mock classes
require_once dirname(__DIR__) . '/tests/mocks/elementor-mock.php';
require_once dirname(__DIR__) . '/tests/mocks/gravity-forms-mock.php';

// Mock essential WordPress functions that our plugin uses
if (!function_exists('did_action')) {
    function did_action($hook) {
        return true; // Mock that hooks have been called
    }
}

if (!function_exists('class_exists')) {
    function class_exists($class) {
        return true; // Mock that classes exist
    }
}

if (!function_exists('version_compare')) {
    function version_compare($version1, $version2, $operator = null) {
        return \version_compare($version1, $version2, $operator);
    }
}

if (!function_exists('esc_html__')) {
    function esc_html__($text, $domain = 'default') {
        return $text; // Just return the text for testing
    }
}

if (!function_exists('esc_html')) {
    function esc_html($text) {
        return htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
    }
}

if (!function_exists('plugins_url')) {
    function plugins_url($path = '', $plugin = '') {
        return 'http://example.com/wp-content/plugins/' . $path;
    }
}

if (!function_exists('add_action')) {
    function add_action($hook, $function, $priority = 10, $accepted_args = 1) {
        return true; // Mock successful hook registration
    }
}

if (!function_exists('wp_register_style')) {
    function wp_register_style($handle, $src, $deps = array(), $ver = false, $media = 'all') {
        return true;
    }
}

if (!function_exists('wp_enqueue_style')) {
    function wp_enqueue_style($handle, $src = '', $deps = array(), $ver = false, $media = 'all') {
        return true;
    }
}

// Load the main plugin functions (but not the full plugin initialization)
// We'll include individual functions as needed in tests

// Include the main plugin file functions without executing the initialization
$plugin_content = file_get_contents(dirname(__DIR__) . '/index.php');

// Extract just the function definitions (not the initialization calls)
$functions_to_extract = [
    'gf_elementor_widget_check_dependencies',
    'gf_elementor_widget_check_elementor_version', 
    'gf_elementor_widget_check_php_version',
    'gf_elementor_widget_admin_notice_missing_dependencies',
    'gf_elementor_widget_admin_notice_minimum_elementor_version',
    'gf_elementor_widget_admin_notice_minimum_php_version',
    'gf_elementor_widget_init',
    'register_gravity_form_elementor_widget',
    'gf_register_widget_styles'
];

// Use regex to extract function definitions
foreach ($functions_to_extract as $function_name) {
    $pattern = '/function\s+' . preg_quote($function_name) . '\s*\([^{]*\{(?:[^{}]*\{[^{}]*\})*[^{}]*\}/s';
    if (preg_match($pattern, $plugin_content, $matches)) {
        eval('?>' . $matches[0]);
    }
}
