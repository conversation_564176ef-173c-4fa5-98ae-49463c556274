<?php
/**
 * Unit test class for the OpeningBraceSameLine sniff.
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2006-2015 Squiz Pty Ltd (ABN **************)
 * @license   https://github.com/PHPCSStandards/PHP_CodeSniffer/blob/master/licence.txt BSD Licence
 */

namespace PHP_CodeSniffer\Standards\Generic\Tests\Classes;

use PHP_CodeSniffer\Tests\Standards\AbstractSniffUnitTest;

/**
 * Unit test class for the OpeningBraceSameLine sniff.
 *
 * @covers \PHP_CodeSniffer\Standards\Generic\Sniffs\Classes\OpeningBraceSameLineSniff
 */
final class OpeningBraceSameLineUnitTest extends AbstractSniffUnitTest
{


    /**
     * Returns the lines where errors should occur.
     *
     * The key of the array should represent the line number and the value
     * should represent the number of errors that should occur on that line.
     *
     * @return array<int, int>
     */
    public function getErrorList()
    {

        return [
            19  => 2,
            23  => 1,
            28  => 2,
            34  => 1,
            38  => 1,
            41  => 1,
            44  => 1,
            47  => 1,
            70  => 1,
            79  => 1,
            90  => 1,
            94  => 1,
            100 => 1,
        ];

    }//end getErrorList()


    /**
     * Returns the lines where warnings should occur.
     *
     * The key of the array should represent the line number and the value
     * should represent the number of warnings that should occur on that line.
     *
     * @return array<int, int>
     */
    public function getWarningList()
    {
        return [51 => 1];

    }//end getWarningList()


}//end class
