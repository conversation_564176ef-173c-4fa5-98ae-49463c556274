<?php
/**
 * Unit test class for the UselessOverridingMethod sniff.
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2007-2014 <PERSON>. All rights reserved.
 * @license   https://github.com/PHPCSStandards/PHP_CodeSniffer/blob/master/licence.txt BSD Licence
 */

namespace PHP_CodeSniffer\Standards\Generic\Tests\CodeAnalysis;

use PHP_CodeSniffer\Tests\Standards\AbstractSniffUnitTest;

/**
 * Unit test class for the UselessOverridingMethod sniff.
 *
 * @covers \PHP_CodeSniffer\Standards\Generic\Sniffs\CodeAnalysis\UselessOverridingMethodSniff
 */
final class UselessOverridingMethodUnitTest extends AbstractSniffUnitTest
{


    /**
     * Returns the lines where errors should occur.
     *
     * The key of the array should represent the line number and the value
     * should represent the number of errors that should occur on that line.
     *
     * @return array<int, int>
     */
    public function getErrorList()
    {
        return [];

    }//end getErrorList()


    /**
     * Returns the lines where warnings should occur.
     *
     * The key of the array should represent the line number and the value
     * should represent the number of warnings that should occur on that line.
     *
     * @param string $testFile The name of the file being tested.
     *
     * @return array<int, int>
     */
    public function getWarningList($testFile='')
    {
        switch ($testFile) {
        case 'UselessOverridingMethodUnitTest.1.inc':
            return [
                4   => 1,
                16  => 1,
                38  => 1,
                56  => 1,
                68  => 1,
                72  => 1,
                93  => 1,
                116 => 1,
                134 => 1,
                146 => 1,
                153 => 1,
            ];
        default:
            return [];
        }

    }//end getWarningList()


}//end class
