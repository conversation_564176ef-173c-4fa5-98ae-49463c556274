<documentation title="Function Call Argument Spacing">
    <standard>
    <![CDATA[
    There should be no space before and exactly one space, or a new line, after a comma when passing arguments to a function or method.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: No space before and exactly one space after a comma.">
        <![CDATA[
foo($bar,<em> </em>$baz);
        ]]>
        </code>
        <code title="Invalid: A space before and no space after a comma.">
        <![CDATA[
foo($bar<em> </em>,<em></em>$baz);
        ]]>
        </code>
    </code_comparison>
</documentation>
