<documentation title="Opening Function Brace <PERSON><PERSON><PERSON>">
    <standard>
    <![CDATA[
    The function opening brace must be on the same line as the end of the function declaration, with
    exactly one space between the end of the declaration and the brace. The brace must be the last
    content on the line.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: Opening brace on the same line.">
        <![CDATA[
function fooFunction($arg1, $arg2 = '')<em> {</em>
    // Do something.
}
        ]]>
        </code>
        <code title="Invalid: Opening brace on the next line.">
        <![CDATA[
function fooFunction($arg1, $arg2 = '')
<em>{</em>
    // Do something.
}
        ]]>
        </code>
    </code_comparison>
</documentation>
