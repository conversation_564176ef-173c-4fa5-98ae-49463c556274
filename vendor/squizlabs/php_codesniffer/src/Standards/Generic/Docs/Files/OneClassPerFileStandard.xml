<documentation title="One Class Per File">
    <standard>
    <![CDATA[
    There should only be one class defined in a file.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: Only one class in the file.">
        <![CDATA[
<?php
<em>class Foo</em>
{
}
        ]]>
        </code>
        <code title="Invalid: Multiple classes defined in one file.">
        <![CDATA[
<?php
<em>class Foo</em>
{
}

<em>class Bar</em>
{
}
        ]]>
        </code>
    </code_comparison>
</documentation>
