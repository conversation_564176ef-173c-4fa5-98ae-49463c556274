<documentation title="Constructor name">
    <standard>
    <![CDATA[
    Constructors should be named __construct, not after the class.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: The constructor is named __construct.">
        <![CDATA[
class Foo
{
    function <em>__construct</em>()
    {
    }
}
        ]]>
        </code>
        <code title="Invalid: The old style class name constructor is used.">
        <![CDATA[
class Foo
{
    function <em>Foo</em>()
    {
    }
}
        ]]>
        </code>
    </code_comparison>
</documentation>
