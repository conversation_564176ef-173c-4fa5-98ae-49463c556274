add-issues-wo-labels=true
add-pr-wo-labels=true
author=true
breaking-labels=backwards-incompatible,Backwards incompatible,breaking
breaking-prefix=### Breaking changes
bug-labels=bug - confirmed
bug-prefix=### Fixes
compare-link=true
date-format=%Y-%m-%d
deprecated-labels=deprecated,Deprecated,Type: Deprecated
deprecated-prefix=### Deprecates
enhancement-labels=improvement,documentation,builds / deploys / releases,feature request
enhancement-prefix=### Changes
exclude-labels=bug - unconfirmed,can't reproduce / won't fix,invalid,triage
filter-issues-by-milestone=true
header=
http-cache=true
issues=true
issue-prefix=### Closes
merge-prefix=### Pull request(s) without label
output=
project=phpcodesniffer-composer-installer
pulls=true
removed-labels=removed,Removed,Type: Removed
removed-prefix=### Removes
security-labels=security,Security,Type: Security
security-prefix=### Security
summary-labels=Release summary,release-summary,Summary,summary
unreleased=true
unreleased-label=Unreleased
unreleased-only=true
user=Dealerdirect
usernames-as-github-logins=true
verbose=false
