<?php

// autoload_psr4.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'PhpParser\\' => array($vendorDir . '/nikic/php-parser/lib/PhpParser'),
    'Mo<PERSON>y\\' => array($vendorDir . '/mockery/mockery/library/Mockery'),
    'GravityFormElementor\\Tests\\' => array($baseDir . '/tests'),
    'GravityFormElementor\\' => array($baseDir . '/src'),
    'Doctrine\\Instantiator\\' => array($vendorDir . '/doctrine/instantiator/src/Doctrine/Instantiator'),
    'DeepCopy\\' => array($vendorDir . '/myclabs/deep-copy/src/DeepCopy'),
    'Dealerdirect\\Composer\\Plugin\\Installers\\PHPCodeSniffer\\' => array($vendorDir . '/dealerdirect/phpcodesniffer-composer-installer/src'),
    'Brain\\Monkey\\' => array($vendorDir . '/brain/monkey/src'),
);
